package com.trs.ai.moye.monitor.response;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 监控事件周期的查询请求<br/>
 * 监控事件，即 积压、断流
 *
 * <AUTHOR>
 * @since 2025/03/207
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorEventPeriodResponse {

    /**
     * 周期开始时间
     */
    private LocalDateTime startTime;

    /**
     * 周期结束时间
     */
    private LocalDateTime endTime;

    /**
     * 周期内发生的监测次数
     */
    private Long monitorCount;

    /**
     * 周期内积压峰值
     */
    private Long peakValue;

    /**
     * 周期内积压峰值时间
     */
    private LocalDateTime peakTime;
}
