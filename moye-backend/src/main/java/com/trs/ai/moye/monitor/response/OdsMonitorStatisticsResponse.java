package com.trs.ai.moye.monitor.response;

import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-30 11:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OdsMonitorStatisticsResponse {

    private Integer id;

    private String name;

    /**
     * 监控总次数
     */
    private String amount;

    /**
     * 异常发生次数：积压次数，断流次数
     */
    private Long increment;

    /**
     * 当前积压量
     */
    private Long lastLag;

    private String proportion;

    private Long totalTaskExecutionTime;

    /**
     * 数据源类型一级分类
     */
    private DataSourceCategory sourceType;
    /**
     * 数据源类型二级分类
     */
    private String sourceSubType;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    /**
     * 从MonitorBatchTaskResponse转换为OdsMonitorStatisticsResponse
     *
     * @param monitorBatchTaskResponse MonitorBatchTaskResponse
     * @return OdsMonitorStatisticsResponse
     */
    public static OdsMonitorStatisticsResponse from(MonitorBatchTaskResponse monitorBatchTaskResponse) {
        OdsMonitorStatisticsResponse odsMonitorStatisticsResponse = new OdsMonitorStatisticsResponse();
        odsMonitorStatisticsResponse.setId(monitorBatchTaskResponse.getId());
        odsMonitorStatisticsResponse.setName(monitorBatchTaskResponse.getName());
        odsMonitorStatisticsResponse.setAmount(monitorBatchTaskResponse.getAmount());
        odsMonitorStatisticsResponse.setIncrement(monitorBatchTaskResponse.getIncrement());
        odsMonitorStatisticsResponse.setTotalTaskExecutionTime(monitorBatchTaskResponse.getIncrement());
        odsMonitorStatisticsResponse.setStartTime(monitorBatchTaskResponse.getStartTime());
        odsMonitorStatisticsResponse.setEndTime(monitorBatchTaskResponse.getEndTime());
        return odsMonitorStatisticsResponse;
    }
}
