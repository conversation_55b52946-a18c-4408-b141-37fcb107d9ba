package com.trs.ai.moye.monitor.response;

import com.trs.ai.moye.monitor.dto.BasicComponentDetectionStatisticsDTO;
import com.trs.ai.moye.monitor.entity.BaseComponentInfo;
import com.trs.ai.moye.monitor.enums.MoyeBaseComponentEnum;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基础组件监控统计
 *
 * <AUTHOR>
 * @since 2025/6/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasicComponentDetectionStatisticsResponse {

    /**
     * 组件名称
     */
    private String componentName;

    /**
     * 组件类型
     */
    private String componentType;

    /**
     * 异常次数
     */
    private long errorCount;

    /**
     * 检测次数
     */
    private long detectionCount;

    /**
     * 最近一次检测状态
     */
    private int lastStatus;

    /**
     * 最近一次异常时间
     */
    private LocalDateTime lastErrorTime;

    /**
     * 最近一次检测时间
     */
    private LocalDateTime lastDetectionTime;

    /**
     * 最近一次异常信息
     */
    private String lastErrorMessage;

    /**
     * 自动检测周期
     */
    private String autoCheckTime;

    /**
     * 影响范围
     */
    private String influenceScope;

    /**
     * 组件描述
     */
    private String description;

    /**
     * 是否开启
     */
    private Boolean autoTestEnabled;

    /**
     * 从基础组件信息和基础组件监控统计中构建基础组件监控统计响应
     * 缺失componentType
     *
     * @param componentInfo                     基础组件信息
     * @param basicComponentDetectionStatistics 基础组件监控统计
     * @return 基础组件监控统计响应
     */
    public static BasicComponentDetectionStatisticsResponse from(BaseComponentInfo componentInfo,
        BasicComponentDetectionStatisticsDTO basicComponentDetectionStatistics) {
        BasicComponentDetectionStatisticsResponse statisticsResponse = new BasicComponentDetectionStatisticsResponse();

        statisticsResponse.setComponentName(componentInfo.getComponentName());
        statisticsResponse.setAutoCheckTime(componentInfo.getAutoCheckTime());
        statisticsResponse.setInfluenceScope(componentInfo.getInfluenceScope());
        statisticsResponse.setDescription(componentInfo.getDescription());
        statisticsResponse.setAutoTestEnabled(componentInfo.isEnabled());

        // 设置组件类型, 优先显示数据库中的
        if (componentInfo.getComponentType() != null) {
            statisticsResponse.setComponentType(componentInfo.getComponentType().getDescription());
        } else {
            MoyeBaseComponentEnum moyeBaseComponent = MoyeBaseComponentEnum.getEnumByName(componentInfo.getComponentName());
            if (moyeBaseComponent != null) {
                statisticsResponse.setComponentType(moyeBaseComponent.getType().getDescription());
            }
        }

        if (basicComponentDetectionStatistics == null) {
            return statisticsResponse;
        }
        statisticsResponse.setErrorCount(basicComponentDetectionStatistics.getErrorCount());
        statisticsResponse.setDetectionCount(basicComponentDetectionStatistics.getDetectionCount());
        statisticsResponse.setLastStatus(basicComponentDetectionStatistics.getLastStatus());
        statisticsResponse.setLastErrorTime(basicComponentDetectionStatistics.getLastErrorTime());
        statisticsResponse.setLastDetectionTime(basicComponentDetectionStatistics.getLastDetectionTime());
        statisticsResponse.setLastErrorMessage(basicComponentDetectionStatistics.getLastErrorMessage());

        return statisticsResponse;
    }
}
