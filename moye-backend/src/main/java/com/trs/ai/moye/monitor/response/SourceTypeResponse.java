package com.trs.ai.moye.monitor.response;

import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/25
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SourceTypeResponse {

    private String key;
    private String value;

    /**
     * 构造函数
     *
     * @param key 数据源类型
     */
    public SourceTypeResponse(DataSourceCategory key) {
        this.key = key.name();
        this.value = key.getLabel();
    }

    /**
     * 构造函数
     *
     * @param type 数据源类型
     */
    public SourceTypeResponse(ConnectionType type) {
        this.key = type.name();
        this.value = type.name();
    }

}
