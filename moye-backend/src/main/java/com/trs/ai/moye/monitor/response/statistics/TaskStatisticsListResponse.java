package com.trs.ai.moye.monitor.response.statistics;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 任务统计列表
 *
 * <AUTHOR>
 * @since 2024/8/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskStatisticsListResponse {

    /**
     * 任务id
     */
    private Integer dataModelId;

    /**
     * 任务名字
     */
    private String name;

    /**
     * 接入方式
     */
    private String mode;

    /**
     * 调度总量
     */
    private Long accessCount;

    /**
     * 调度正常数
     */
    private Long successCount;

    /**
     * 调度异常数
     */
    private Long errorCount;

}
