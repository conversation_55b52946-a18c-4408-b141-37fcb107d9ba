package com.trs.ai.moye.monitor.response;

import com.trs.ai.moye.common.dao.GroupCount;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 仪表盘类型的监控数据返回类
 *
 * <AUTHOR>
 * @since 2024/10/25 19:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DashboardResponse {

    /**
     * 数据总量
     */
    private Long total;

    /**
     * 分类统计
     */
    private List<Item> items;

    /**
     * 存储总数(目前只计算了要素库)
     */
    private Long storageCount;


    /**
     * 仪表盘分类
     */
    @Data
    public static class Item {

        /**
         * 分类标签名称
         */
        private String name;

        /**
         * 分类数量
         */
        private Long count;

        /**
         * 枚举值
         */
        private Object value;

        /**
         * 创建 Item 实例的静态方法
         *
         * @param name  分类名称
         * @param count 分类数量
         * @return Item 实例
         */
        public static Item createItem(String name, Long count) {
            Item item = new Item();
            item.setName(name);
            item.setCount(count);
            return item;
        }

        /**
         * 创建 Item 实例的静态方法，包含枚举值
         *
         * @param name  分类名称
         * @param count 分类数量
         * @param value 枚举值
         * @return Item 实例
         */
        public static Item createItem(String name, Long count, Object value) {
            Item item = createItem(name, count);
            item.setValue(value);
            return item;
        }
    }

    /**
     * 根据 GroupCount 生成 DashboardResponse
     *
     * @param groupCounts 分组统计结果
     * @param valueName   分组值名称
     * @param <T>         分组值类型 {@link GroupCount#getValue()}
     */
    public <T> DashboardResponse(List<GroupCount<T>> groupCounts, Function<T, String> valueName) {
        this.items = new ArrayList<>();
        this.total = groupCounts.stream().mapToLong(groupCount -> {
            Item item = new Item();
            item.setName(valueName.apply(groupCount.getValue()));
            item.setCount(groupCount.getCount());
            item.setValue(groupCount.getValue());
            items.add(item);
            return groupCount.getCount();
        }).sum();
    }

    /**
     * 专门处理String类型GroupCount的构造函数，默认包含枚举值
     *
     * @param groupCounts  String类型的分组统计结果
     * @param storageCount 存储总数
     */
    public DashboardResponse(List<GroupCount<String>> groupCounts, Long storageCount) {
        this.items = new ArrayList<>();
        this.total = groupCounts.stream().mapToLong(groupCount -> {
            Item item = new Item();
            item.setName(groupCount.getValue());
            item.setCount(groupCount.getCount());
            item.setValue(groupCount.getValue());
            items.add(item);
            return groupCount.getCount();
        }).sum();
        this.storageCount = storageCount;
    }

    /**
     * 专门处理String类型GroupCount的构造函数，默认包含枚举值，storageCount为0
     *
     * @param groupCounts String类型的分组统计结果
     */
    public DashboardResponse(List<GroupCount<String>> groupCounts) {
        this(groupCounts, 0L);
    }
}
