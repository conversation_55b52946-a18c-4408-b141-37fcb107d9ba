package com.trs.ai.moye.monitor.response;

import com.trs.ai.moye.monitor.entity.DataModelMonitorBaseInfo;
import com.trs.moye.base.monitor.entity.MonitorBatchTask;
import com.trs.moye.base.monitor.entity.MonitorOdsCutoff;
import com.trs.moye.base.monitor.entity.MonitorOdsFluctuation;
import com.trs.moye.base.monitor.entity.MonitorOdsLag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/29
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MonitorDetailTableResponse extends DataModelMonitorBaseInfo {

    /*
    ==========================================================
                          断流监控的字段
    ==========================================================
     */
    /**
     * 监控值：监控值
     */
    private Long monitorValue;


    /**
     * 增量字段：增量字段
     */
    private String incrementColumn;

    /**
     * 增量字段值：增量字段值
     */
    private String incrementValue;

    /**
     * 监控详情：监控详情：对于监控数据的补充说明，可以为空
     */
    private String monitorDetail;

    /**
     * 是否断流：是否断流：1-断流，0-不断流；监控值与上一次比较无变化被认为是断流，否则不是断流（数据库用数字，代码用布尔）
     */
    private Boolean isCutoff;

    /*
    ==========================================================
                           积压监控的字段
    ==========================================================
     */

    /**
     * 当前最新的offset
     */
    private Long offset;

    /**
     * 当前总量
     */
    private Long total;
    /**
     * 积压量
     */
    private Long lag;
    /**
     * 是否积压
     */
    private Boolean isLag;

    /*
    ==========================================================
                           波动监控的字段
    ==========================================================
     */

    /**
     * 本周期数据增加量
     */
    private Long increment;
    /**
     * 平均值
     */
    private Long average;
    /**
     * 波动类型
     */
    private Integer fluctuationType;
    /**
     * 波动值
     */
    private Integer fluctuation;
    /**
     * 是否超阈波动
     */
    private Boolean isFluctuation;


    /**
     * 从MonitorOdsLagRecord复制属性
     *
     * @param lagRecord 源对象
     * @return 复制后的对象
     */
    public static MonitorDetailTableResponse from(MonitorOdsLag lagRecord) {
        MonitorDetailTableResponse detailTableResponse = new MonitorDetailTableResponse();
        copy(lagRecord, detailTableResponse);
        detailTableResponse.setOffset(lagRecord.getOffset());
        detailTableResponse.setTotal(lagRecord.getTotal());
        detailTableResponse.setLag(lagRecord.getLag());
        detailTableResponse.setIsLag(lagRecord.getIsLag());
        return detailTableResponse;
    }

    /**
     * 从MonitorOdsCutoff复制属性
     *
     * @param cutoff 源对象
     * @return 复制后的对象
     */
    public static MonitorDetailTableResponse from(MonitorOdsCutoff cutoff) {
        MonitorDetailTableResponse detailTableResponse = new MonitorDetailTableResponse();
        copy(cutoff, detailTableResponse);
        detailTableResponse.setMonitorValue(cutoff.getMonitorValue());
        detailTableResponse.setIncrementColumn(cutoff.getIncrementColumn());
        detailTableResponse.setIncrementValue(cutoff.getIncrementValue());
        detailTableResponse.setMonitorDetail(cutoff.getMonitorDetail());
        detailTableResponse.setIsCutoff(cutoff.isCutoff());
        return detailTableResponse;
    }

    /**
     * 从MonitorOdsFluctuation复制属性
     *
     * @param fluctuation 源对象
     * @return 复制后的对象
     */
    public static MonitorDetailTableResponse from(MonitorOdsFluctuation fluctuation) {
        MonitorDetailTableResponse detailTableResponse = new MonitorDetailTableResponse();
        copy(fluctuation, detailTableResponse);
        detailTableResponse.setIncrement(fluctuation.getIncrement());
        detailTableResponse.setAverage(fluctuation.getAverage());
        detailTableResponse.setFluctuationType(fluctuation.getFluctuationType());
        detailTableResponse.setFluctuation(fluctuation.getFluctuation());
        detailTableResponse.setIsFluctuation(fluctuation.isThresholdExceeded());
        return detailTableResponse;
    }

    /**
     * 从MonitorBatchTask复制属性
     *
     * @param monitorBatchTask 源对象
     * @return 复制后的对象
     */
    public static MonitorDetailTableResponse from(MonitorBatchTask monitorBatchTask) {
        MonitorDetailTableResponse detailTableResponse = new MonitorDetailTableResponse();
        detailTableResponse.setId(monitorBatchTask.getId());
        detailTableResponse.setDataModelId(monitorBatchTask.getDataModelId());
        detailTableResponse.setDataModelName(monitorBatchTask.getDataModelName());
        detailTableResponse.setMonitorValue(monitorBatchTask.getMonitorValue());
        detailTableResponse.setMonitorTime(monitorBatchTask.getMonitorTime());
        return detailTableResponse;
    }
}
