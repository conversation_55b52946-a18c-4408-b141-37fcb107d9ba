package com.trs.ai.moye.monitor.response;

import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.moye.base.monitor.entity.TodayMonitorMetric;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-05-26 19:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class DataProcessTodayMonitorResponse extends TodayMonitorMetric {

    private boolean isEnable;

    public DataProcessTodayMonitorResponse(TodayMonitorMetric monitorRecord, boolean isEnable) {
        BeanUtil.copyInheritProperties(monitorRecord, this);
        this.isEnable = isEnable;
    }
}
