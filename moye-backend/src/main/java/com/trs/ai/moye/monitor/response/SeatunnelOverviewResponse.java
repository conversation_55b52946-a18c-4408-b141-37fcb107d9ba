package com.trs.ai.moye.monitor.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * seatunnel集群信息
 *
 * <AUTHOR>
 * @since 2025/2/26
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SeatunnelOverviewResponse {

    /**
     * Seatunnel 项目的版本号
     */
    private String projectVersion;

    /**
     * 当前运行版本的 Git 提交简写
     */
    private String gitCommitAbbrev;

    /**
     * 集群中总的插槽（slot）数量
     */
    private String totalSlot;

    /**
     * 未分配的插槽数量
     */
    private String unassignedSlot;

    /**
     * 当前正在运行的作业数量
     */
    private String runningJobs;

    /**
     * 已完成的作业数量
     */
    private String finishedJobs;

    /**
     * 失败的作业数量
     */
    private String failedJobs;

    /**
     * 取消的作业数量
     */
    private String canceledJobs;

    /**
     * 当前正在运行的工作节点的数量
     */
    private String workers;

}
