package com.trs.ai.moye.monitor.response;

import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.moye.base.monitor.entity.TodayMonitorMetric;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-05-26 19:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class DataStorageTodayMonitorResponse extends TodayMonitorMetric {

    private String connectionName;

    private boolean isEnable;

    public DataStorageTodayMonitorResponse(TodayMonitorMetric monitorRecord, String connectionName, boolean isEnable) {
        BeanUtil.copyInheritProperties(monitorRecord, this);
        this.connectionName = connectionName;
        this.isEnable = isEnable;
    }
}
