package com.trs.ai.moye.monitor.response;

import com.trs.moye.base.common.request.TimeRangeParams;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/3/14
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorTrendDetailResponse {

    private LocalDateTime startTime;

    private Long value;

    private List<MonitorTrendResponse> trends;


    /**
     * 创建MonitorTrendDetailResponse
     *
     * @param timeRangeParams 时间范围参数
     * @param startTime       开始时间
     * @param value           值
     * @param trends          趋势
     * @return MonitorTrendDetailResponse
     */
    public static MonitorTrendDetailResponse of(TimeRangeParams timeRangeParams, LocalDateTime startTime, Long value,
        List<MonitorTrendResponse> trends) {
        MonitorTrendDetailResponse response = new MonitorTrendDetailResponse();
        if (Objects.nonNull(startTime) && timeRangeParams.getMinTime().isAfter(startTime)) {
            response.setStartTime(startTime);
            response.setValue(value);
        }
        response.setTrends(trends);
        return response;
    }
}
