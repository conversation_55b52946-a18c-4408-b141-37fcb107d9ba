package com.trs.ai.moye.monitor.response;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批处理任务监控信息
 *
 * <AUTHOR>
 * @since 2025/3/4
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorBatchTaskResponse {

    private Integer id;

    private String name;
    /**
     * 监控总次数
     */
    private String amount;

    private Long increment;


    private LocalDateTime startTime;

    private LocalDateTime endTime;

}
