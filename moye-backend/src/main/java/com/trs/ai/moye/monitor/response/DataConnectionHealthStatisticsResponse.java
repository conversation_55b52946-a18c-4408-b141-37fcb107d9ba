package com.trs.ai.moye.monitor.response;

import com.trs.ai.moye.monitor.dto.DataConnectionHealthStatisticsDto;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.DataConnectionMonitorConfig;
import com.trs.moye.base.data.connection.entity.params.ConnectionParams;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据源监控统计
 *
 * <AUTHOR>
 * @since 2025/7/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataConnectionHealthStatisticsResponse {

    /**
     * id
     */
    private Integer connectionId;

    /**
     * 数据连接名称
     */
    private String connectionName;

    /**
     * 数据连接类型
     */
    private String connectionType;

    /**
     * 是否开启自动检测
     */
    private Boolean autoTestEnabled;

    /**
     * 连接信息
     */
    private ConnectionParams connectionParams;

    /**
     * 异常次数
     */
    private Long errorCount;

    /**
     * 检测次数
     */
    private Long detectionCount;

    /**
     * 最近一次检测状态
     */
    private Boolean lastStatus;

    /**
     * 最近一次异常时间
     */
    private LocalDateTime lastErrorTime;

    /**
     * 最近一次检测时间
     */
    private LocalDateTime lastDetectionTime;

    /**
     * 最近一次异常信息
     */
    private String lastErrorMessage;

    /**
     * 构造response
     *
     * @param dataConnection                    数据源
     * @param dataConnectionHealthStatisticsDto 统计信息
     * @return response
     */
    public static DataConnectionHealthStatisticsResponse from(DataConnection dataConnection,
        DataConnectionHealthStatisticsDto dataConnectionHealthStatisticsDto) {

        DataConnectionHealthStatisticsResponse response = new DataConnectionHealthStatisticsResponse();

        // 基本信息
        response.setConnectionId(dataConnection.getId());
        response.setConnectionName(dataConnection.getName());
        response.setConnectionType(dataConnection.isSource() ? "数据源" : "数据存储");
        response.setAutoTestEnabled(
            Optional.ofNullable(dataConnection.getMonitorConfig()).map(DataConnectionMonitorConfig::isEnabled)
                .orElse(false));
        response.setConnectionParams(dataConnection.getConnectionParams());

        // 统计信息
        if (dataConnectionHealthStatisticsDto == null) {
            response.setErrorCount(0L);
            response.setDetectionCount(0L);
            return response;
        }
        response.setErrorCount(dataConnectionHealthStatisticsDto.getErrorCount());
        response.setDetectionCount(dataConnectionHealthStatisticsDto.getDetectionCount());
        response.setLastStatus(!Boolean.TRUE.equals(dataConnectionHealthStatisticsDto.getLastIsError()));
        response.setLastErrorTime(dataConnectionHealthStatisticsDto.getLastErrorTime());
        response.setLastDetectionTime(dataConnectionHealthStatisticsDto.getLastDetectionTime());
        response.setLastErrorMessage(dataConnectionHealthStatisticsDto.getLastErrorMessage());
        return response;
    }
}
