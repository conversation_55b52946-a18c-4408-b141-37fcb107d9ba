package com.trs.ai.moye.monitor.response;

import com.trs.moye.base.monitor.entity.DataModelMonitorConfig;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-08-30 13:55
 */
@Data
public class OdsMonitorConfigVersionResponse {

    private Integer id;

    private String name;

    private LocalDateTime startTime;

    private LocalDateTime endTime;


    /**
     * 转换
     *
     * @param dataModelMonitorConfig DataModelMonitorConfig
     * @return OdsMonitorConfigVersionVO
     */
    public static OdsMonitorConfigVersionResponse from(DataModelMonitorConfig dataModelMonitorConfig) {
        OdsMonitorConfigVersionResponse odsMonitorStatisticsVO = new OdsMonitorConfigVersionResponse();
        odsMonitorStatisticsVO.setId(dataModelMonitorConfig.getId());
        odsMonitorStatisticsVO.setName(dataModelMonitorConfig.getVersion());
        odsMonitorStatisticsVO.setStartTime(dataModelMonitorConfig.getCreateTime());
        odsMonitorStatisticsVO.setEndTime(dataModelMonitorConfig.getUpdateTime());
        return odsMonitorStatisticsVO;
    }
}
