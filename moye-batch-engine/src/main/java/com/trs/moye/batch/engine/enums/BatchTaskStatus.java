package com.trs.moye.batch.engine.enums;

import org.apache.hadoop.yarn.api.records.FinalApplicationStatus;
import org.apache.hadoop.yarn.api.records.YarnApplicationState;

/**
 * BatchTaskStatus
 *
 * <AUTHOR>
 * @since 2025/7/28 17:20
 */
public enum BatchTaskStatus {
    /**
     * 等待中
     */
    WAITING,
    /**
     * 提交中 - 任务已触发，正在提交到集群的过程中
     */
    SUBMITTING,
    /**
     * 运行中
     */
    RUNNING,
    /**
     * 成功
     */
    SUCCESS,
    /**
     * 失败
     */
    FAILED,
    /**
     * 被丢弃
     */
    DISCARDED,
    /**
     * 被终止
     */
    KILLED,
    ;

    /**
     * 将Yarn应用状态映射为任务状态，同时考虑最终应用状态
     *
     * @param yarnState   Yarn应用状态
     * @param finalStatus 最终应用状态
     * @return 对应的任务状态，如果是活动状态则返回null
     */
    public static BatchTaskStatus fromYarnState(YarnApplicationState yarnState, FinalApplicationStatus finalStatus) {
        if (yarnState == null) {
            return null;
        }

        // 处理已结束的状态
        switch (yarnState) {
            case FINISHED:
                return mapFinishedStatus(finalStatus);
            case FAILED:
                return FAILED;
            case KILLED:
                return KILLED;
            default:
                break;
        }

        // 处理活动状态
        switch (yarnState) {
            case RUNNING:
            case SUBMITTED:
            case ACCEPTED:
                // 这些是活动状态，不直接映射，由调用方判断是否为孤儿任务
                return null;
            default:
                return DISCARDED;
        }
    }

    /**
     * 映射FINISHED状态的最终应用状态
     *
     * @param finalStatus 最终应用状态
     * @return 对应的任务状态
     */
    private static BatchTaskStatus mapFinishedStatus(FinalApplicationStatus finalStatus) {
        if (finalStatus == null) {
            // 没有FinalApplicationStatus时默认返回SUCCESS
            return SUCCESS;
        }

        switch (finalStatus) {
            case SUCCEEDED:
                return SUCCESS;
            case FAILED:
                return FAILED;
            case KILLED:
                return KILLED;
            case UNDEFINED:
            default:
                // 其他FinalApplicationStatus状态，默认返回SUCCESS
                return SUCCESS;
        }
    }

    /**
     * 将Standalone应用状态字符串映射为任务状态
     *
     * @param appState Standalone应用状态字符串
     * @return 对应的任务状态，如果是未知状态则返回DISCARDED
     */
    public static BatchTaskStatus fromStandaloneState(String appState) {
        if (appState == null) {
            return DISCARDED;
        }

        switch (appState.toUpperCase()) {
            case "FINISHED":
                return SUCCESS;
            case "FAILED":
                return FAILED;
            case "KILLED":
                return KILLED;
            case "RUNNING":
                return RUNNING;
            case "UNKNOWN":
            default:
                return DISCARDED;
        }
    }
}
