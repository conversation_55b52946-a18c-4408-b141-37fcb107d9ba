package com.trs.moye.batch.engine.utils;

import com.trs.moye.batch.engine.dao.clickhouse.BatchTaskRecordMapper;
import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import com.trs.moye.batch.engine.enums.BatchTaskStatus;
import com.trs.moye.batch.engine.service.BatchTaskMonitor;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务状态更新工具类 - 统一处理任务状态更新和追踪记录
 *
 * <AUTHOR>
 * @since 2025/08/31
 */
@Slf4j
public class TaskStatusUpdateUtils {

    /**
     * 更新任务状态
     *
     * @param task                  任务记录
     * @param targetStatus          目标状态
     * @param reason                更新原因
     * @param batchTaskRecordMapper 任务记录映射器
     * <AUTHOR>
     * @since 2025/09/02 10:57:47
     */
    public static void updateTaskStatus(BatchTaskRecord task, BatchTaskStatus targetStatus,
        String reason, BatchTaskRecordMapper batchTaskRecordMapper) {
        try {
            LocalDateTime now = LocalDateTime.now();

            // 更新任务状态
            BatchTaskRecord updateRecord = BatchTaskRecord.builder()
                .executeId(task.getExecuteId())
                .status(targetStatus)
                .isError(isErrorStatus(targetStatus) ? 1 : 0)
                .endTime(now)
                .build();
            batchTaskRecordMapper.updateSelective(updateRecord);
            log.info("成功更新任务状态 [executeId:{}, oldStatus:{}, newStatus:{}, reason:{}]",
                task.getExecuteId(), task.getStatus(), targetStatus, reason);

        } catch (Exception e) {
            log.error("更新任务状态时发生异常 [executeId:{}, targetStatus:{}]",
                task.getExecuteId(), targetStatus, e);
        }
    }

    /**
     * 判断是否为错误状态
     *
     * @param status 任务状态
     * @return 是否为错误状态
     */
    private static boolean isErrorStatus(BatchTaskStatus status) {
        return status == BatchTaskStatus.DISCARDED || status == BatchTaskStatus.FAILED;
    }

    /**
     * 检查任务是否仍为SUBMITTING状态，如果是且有异常则标记为FAILED
     *
     * @param executeId             任务执行ID
     * @param exception             异常信息
     * @param batchTaskRecordMapper 任务记录映射器
     * <AUTHOR>
     * @since 2025/09/01 10:29:52
     */
    public static void checkAndUpdateSubmittingTaskOnFailure(String executeId, Throwable exception,
        BatchTaskRecordMapper batchTaskRecordMapper) {
        if (exception != null) {
            try {
                BatchTaskRecord currentRecord = batchTaskRecordMapper.selectByExecuteId(executeId);
                if (currentRecord != null && BatchTaskStatus.SUBMITTING.equals(currentRecord.getStatus())) {
                    log.info("任务 executeId: {} 在SUBMITTING状态下失败，更新状态为FAILED", executeId);
                    BatchTaskMonitor.insertOrUpdateRecord(
                        BatchTaskRecord.endTask(executeId, exception,
                            LocalDateTime.now(), 0L, new BatchTaskRecord.TaskWriteCount[0])
                    );
                }
            } catch (Exception e) {
                log.error("检查并更新SUBMITTING任务状态时发生异常", e);
            }
        }
    }
}