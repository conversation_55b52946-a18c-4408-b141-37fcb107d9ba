package com.trs.moye.batch.engine.entity;

import com.trs.moye.ability.entity.operator.BatchOperatorExecutionRecord;
import com.trs.moye.batch.engine.entity.vo.BatchTaskVO;
import com.trs.moye.batch.engine.enums.TaskNodeEnum;
import com.trs.moye.batch.engine.utils.SnowflakeIdUtil;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.exception.ExceptionUtils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-25 15:50
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTaskTracer {

    /**
     * 主键，非自增，入库的时候，需要应用保障唯一性
     */
    private Long id = SnowflakeIdUtil.newId();

    /**
     * 节点
     */
    private String node;

    /**
     * 执行id，主键，定时任务触发时产生一条记录
     */
    private String executeId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 处理时长，单位毫秒
     */
    private Long processTime;

    /**
     * 是否异常：
     */
    private Integer isError;

    /**
     * 是否异常：
     */
    private String errorMsg;

    /**
     * 算子输出数据 字段名
     */
    private List<String> fields;

    /**
     * 算子输出数据 抽样数据
     */
    private List<Map<String, Object>> sampleData;

    /**
     * 算子处理前数据量
     */
    private Long preProcessDataCount = 0L;

    /**
     * 算子输出数据量
     */
    private Long dataCount = 0L;

    /**
     * 算子输出表名
     */
    private String outputTableName;

    /**
     * 输入表名
     */
    private String inputTableName;

    /**
     * 过滤条件
     */
    private String conditions;

    /**
     * 编排算子名称
     */
    private String arrangedName;


    /**
     * 从 BatchTaskVO 获取监控数据, 表明任务开始
     *
     * @param vo        BatchTaskVO
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param e         异常信息
     * @return BatchTaskTracer
     */
    public static BatchTaskTracer beginTask(BatchTaskVO vo, LocalDateTime startTime, LocalDateTime endTime,
        Exception e) {
        return BatchTaskTracer.builder()
            .id(SnowflakeIdUtil.newId())
            .executeId(vo.getExecuteId())
            .startTime(startTime)
            .endTime(endTime)
            .node(TaskNodeEnum.FIRE.getName())
            .arrangedName(TaskNodeEnum.FIRE.getName())
            .isError(Objects.isNull(e) ? BatchTaskRecord.NON_ERROR : BatchTaskRecord.IS_ERROR)
            .errorMsg(Objects.isNull(e) ? "" : ExceptionUtils.getStackTrace(e))
            .processTime(Duration.between(startTime, endTime).toMillis())
            .build();
    }


    /**
     * 等待前序任务结束
     *
     * @param executeId     执行id
     * @param preExecuteId  前序执行id
     * @param retryCount    当前重试次数
     * @param maxRetryCount 最大重试次数
     * @return BatchTaskTracer
     */
    public static BatchTaskTracer waitTask(String executeId, String preExecuteId, Integer retryCount, Integer maxRetryCount) {
        LocalDateTime now = LocalDateTime.now();
        TaskNodeEnum waitNode = TaskNodeEnum.WAIT;
        String message = String.format("等待前序调度结束[执行id:%s]", preExecuteId);
        if (retryCount > 0) {
            message = String.format("第[%s]次重试，等待前序调度[执行id:%s]，前序调度仍未结束，未超过等待重试次数上限[%d]。继续等待。", retryCount, preExecuteId, maxRetryCount);
        }
        return BatchTaskTracer.builder()
            .id(SnowflakeIdUtil.newId())
            .executeId(executeId)
            .startTime(now)
            .endTime(now)
            .node(waitNode.getName())
            .arrangedName(waitNode.getName())
            .isError(BatchTaskRecord.NON_ERROR)
            .errorMsg(message)
            .processTime(0L)
            .build();
    }


    /**
     * 任务结束日志
     *
     * @param executeId 执行id
     * @param e         异常
     * @param endTime   结束时间
     * @return BatchTaskTracer
     */
    public static BatchTaskTracer endTask(String executeId, Throwable e, LocalDateTime endTime) {
        return BatchTaskTracer.builder()
            .id(SnowflakeIdUtil.newId())
            .executeId(executeId)
            .startTime(endTime)
            .endTime(endTime)
            .isError(Objects.isNull(e) ? BatchTaskRecord.NON_ERROR : BatchTaskRecord.IS_ERROR)
            .errorMsg(Objects.isNull(e) ? "" : ExceptionUtils.getStackTrace(e))
            .processTime(0L)
            .node(TaskNodeEnum.END.getName())
            .arrangedName(TaskNodeEnum.END.getName())
            .build();
    }

    /**
     * 标记任务为丢弃状态
     *
     * @param executeId     执行id
     * @param preExecuteId  前序执行id
     * @param retryCount    当前重试次数
     * @param maxRetryCount 最大重试次数
     * @return 丢弃的记录
     */
    public static BatchTaskTracer discardTask(String executeId, String preExecuteId, Integer retryCount, Integer maxRetryCount) {
        LocalDateTime now = LocalDateTime.now();
        TaskNodeEnum node = TaskNodeEnum.DISCARD;
        return BatchTaskTracer.builder()
            .id(SnowflakeIdUtil.newId())
            .executeId(executeId)
            .startTime(now)
            .endTime(now)
            .isError(1)
            .errorMsg(String.format("第[%s]次等待前序调度[执行id:%s]，前序调度仍未结束，超过等待重试次数上限[%d]。不再等待前序调度结束，丢弃当前调度", retryCount, preExecuteId, maxRetryCount))
            .processTime(0L)
            .node(node.getName())
            .arrangedName(node.getName())
            .build();
    }

    /**
     * 任务结束日志
     *
     * @param executeId 执行id
     * @param endTime   结束时间
     * @return BatchTaskTracer
     */
    public static BatchTaskTracer killTask(String executeId, LocalDateTime endTime) {
        return BatchTaskTracer.builder()
            .id(SnowflakeIdUtil.newId())
            .executeId(executeId)
            .startTime(endTime)
            .endTime(endTime)
            .isError(1)
            .errorMsg("手动停止任务")
            .processTime(0L)
            .node(TaskNodeEnum.END.getName())
            .arrangedName(TaskNodeEnum.END.getName())
            .build();
    }

    /**
     * 任务日志
     *
     * @param executeId 执行id
     * @param e         异常
     * @param name      任务名称
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return BatchTaskTracer
     */
    public static BatchTaskTracer task(String executeId, String name, Throwable e, LocalDateTime beginTime,
        LocalDateTime endTime) {
        return BatchTaskTracer.builder()
            .id(SnowflakeIdUtil.newId())
            .executeId(executeId)
            .startTime(beginTime)
            .endTime(endTime)
            .isError(Objects.isNull(e) ? BatchTaskRecord.NON_ERROR : BatchTaskRecord.IS_ERROR)
            .errorMsg(Objects.isNull(e) ? "" : ExceptionUtils.getStackTrace(e))
            .processTime(Duration.between(beginTime, endTime).toMillis())
            .node(name)
            .arrangedName(name)
            .build();
    }


    /**
     * 通过 算子执行记录 构建 BatchTaskTracer
     *
     * @param executeId       执行id
     * @param executionRecord 算子执行记录
     * @return BatchTaskTracer
     */
    public static BatchTaskTracer fromOperatorExecutionRecord(String executeId,
        BatchOperatorExecutionRecord executionRecord) {
        return BatchTaskTracer.builder()
            .id(SnowflakeIdUtil.newId())
            .node(executionRecord.getFunctionName())
            .executeId(executeId)
            .startTime(executionRecord.getStartTime())
            .endTime(executionRecord.getEndTime())
            .processTime(executionRecord.getProcessTime())
            .isError(Boolean.TRUE.equals(executionRecord.getIsError()) ? BatchTaskRecord.IS_ERROR
                : BatchTaskRecord.NON_ERROR)
            .errorMsg(executionRecord.getErrorMsg())
            .preProcessDataCount(executionRecord.getPreCount())
            .dataCount(executionRecord.getCount())
            .outputTableName(executionRecord.getOutputTableName())
            .arrangedName(executionRecord.getArrangedName())
            .inputTableName(executionRecord.getInputTableName())
            .conditions(executionRecord.getConditions())
            .fields(executionRecord.getFields())
            .sampleData(executionRecord.getData())
            .build();
    }

    /**
     * 标记任务为丢失状态
     *
     * @param executeIdList 执行id
     * @return 丢失的记录
     */
    public static List<BatchTaskTracer> lostTask(List<String> executeIdList) {
        LocalDateTime now = LocalDateTime.now();
        return executeIdList.stream()
            .map(executeId -> BatchTaskTracer.builder()
                .id(SnowflakeIdUtil.newId())
                .executeId(executeId)
                .startTime(now)
                .endTime(now)
                .isError(BatchTaskRecord.IS_ERROR)
                .errorMsg(String.format("[%s] batch-engine 停止服务，无法监听当前任务的执行状态。", now))
                .processTime(0L)
                .node(TaskNodeEnum.END.getName())
                .arrangedName(TaskNodeEnum.END.getName())
                .build())
            .collect(Collectors.toList());
    }
}
