package com.trs.moye.batch.engine.service;

import com.trs.moye.batch.engine.dao.clickhouse.BatchTaskRecordMapper;
import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import com.trs.moye.batch.engine.enums.BatchTaskStatus;
import java.time.LocalDateTime;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 任务完成处理服务 - 统一处理任务完成后的所有逻辑
 *
 * <AUTHOR>
 * @since 2025/09/02
 */
@Slf4j
@Service
public class PostCompletionService {

    @Resource
    private BatchTaskRecordMapper batchTaskRecordMapper;

    /**
     * 集中处理任务完成后的所有逻辑，确保操作的原子性和幂等性
     *
     * @param taskRecord  待处理的任务记录
     * @param finalStatus 要更新的最终状态
     * @param reason      状态变更原因
     */
    public synchronized void finalizeTask(BatchTaskRecord taskRecord, BatchTaskStatus finalStatus, String reason) {
        if (taskRecord == null) {
            log.warn("任务记录为空，无法处理完成逻辑");
            return;
        }

        String executeId = taskRecord.getExecuteId();

        // 1. 幂等性检查：如果任务已是终端状态，则直接返回
        if (isTerminalStatus(taskRecord.getStatus())) {
            log.info("任务 [executeId:{}] 已是终端状态 [{}]，无需重复处理", executeId, taskRecord.getStatus());
            return;
        }

        log.info("开始处理任务完成逻辑 [executeId:{}, finalStatus:{}, reason:{}]", executeId, finalStatus, reason);

        try {
            // 2. 更新任务记录
            LocalDateTime now = LocalDateTime.now();
            BatchTaskRecord updateRecord = BatchTaskRecord.builder()
                .executeId(executeId)
                .status(finalStatus)
                .endTime(now)
                .isError(isErrorStatus(finalStatus) ? BatchTaskRecord.IS_ERROR : BatchTaskRecord.NON_ERROR)
                .build();

            batchTaskRecordMapper.updateSelective(updateRecord);
            log.info("任务 [executeId:{}] 状态更新完成 [{} -> {}]", executeId, taskRecord.getStatus(), finalStatus);

            log.info("任务 [executeId:{}] 完成处理逻辑执行完成", executeId);

        } catch (Exception e) {
            log.error("处理任务 [executeId:{}] 完成逻辑时发生异常", executeId, e);
            // 抛出异常让调用方处理
            throw new RuntimeException("处理任务完成逻辑失败: " + e.getMessage(), e);
        }
    }

    /**
     * 判断是否为终端状态
     *
     * @param status 任务状态
     * @return 是否为终端状态
     */
    private boolean isTerminalStatus(BatchTaskStatus status) {
        return status == BatchTaskStatus.SUCCESS
            || status == BatchTaskStatus.FAILED
            || status == BatchTaskStatus.KILLED
            || status == BatchTaskStatus.DISCARDED;
    }

    /**
     * 判断是否为错误状态
     *
     * @param status 任务状态
     * @return 是否为错误状态
     */
    private boolean isErrorStatus(BatchTaskStatus status) {
        return status == BatchTaskStatus.FAILED
            || status == BatchTaskStatus.DISCARDED;
    }
}