package com.trs.moye.batch.engine.service;

import com.trs.ai.ty.base.entity.exception.BizException;
import com.trs.moye.batch.engine.config.HadoopProperties;
import com.trs.moye.batch.engine.enums.BatchTaskStatus;
import com.trs.moye.batch.engine.spark.config.SparkApplicationConfig;
import com.trs.moye.batch.engine.utils.KerberosAuthUtils;
import com.trs.moye.batch.engine.utils.RetryUtils;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.yarn.api.records.ApplicationId;
import org.apache.hadoop.yarn.api.records.ApplicationReport;
import org.apache.hadoop.yarn.api.records.YarnApplicationState;
import org.apache.hadoop.yarn.client.api.YarnClient;
import org.apache.hadoop.yarn.exceptions.ApplicationNotFoundException;
import org.apache.hadoop.yarn.exceptions.YarnException;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

/**
 * 集群查询服务 - 统一封装从不同类型集群获取应用状态的逻辑
 *
 * <AUTHOR>
 * @since 2025/08/30
 */
@Slf4j
@Service
public class ClusterQueryService {

    @Resource
    private SparkApplicationConfig sparkApplicationConfig;
    @Resource
    private HadoopProperties hadoopProperties;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 批量从Yarn获取所有活动应用的状态 该方法使用Yarn REST API一次性获取所有活动应用信息，大幅提高查询效率
     *
     * @param principal  Kerberos认证principal
     * @param keytabPath Kerberos认证keytab文件路径
     * @return 以applicationId为键，应用报告为值的Map
     */
    public Map<String, ApplicationReport> getYarnActiveApplications(String principal, String keytabPath)
        throws IOException, InterruptedException {
        log.debug("开始批量获取Yarn活动应用状态 [authProvided:{}]",
            Objects.nonNull(principal) && Objects.nonNull(keytabPath));

        // 使用RetryUtils执行带重试的批量应用查询
        return RetryUtils.executeWithDefaultRetry("Yarn批量应用状态查询", () -> {
            YarnClient yarnClient = null;
            try {
                // 使用KerberosAuthUtils创建Hadoop配置
                Configuration conf = KerberosAuthUtils.createHadoopConfiguration(hadoopProperties);

                // 使用KerberosAuthUtils执行Kerberos认证
                if (KerberosAuthUtils.isValidKerberosAuth(principal, keytabPath)) {
                    KerberosAuthUtils.authenticateWithKerberos(conf, principal, keytabPath);
                }

                // 创建YarnClient
                yarnClient = YarnClient.createYarnClient();
                yarnClient.init(conf);
                yarnClient.start();

                // 获取所有应用列表
                List<ApplicationReport> applications = yarnClient.getApplications();

                // 过滤出活动状态的应用（RUNNING, SUBMITTED, ACCEPTED）
                List<ApplicationReport> activeApplications = applications.stream()
                    .filter(app -> {
                        YarnApplicationState state = app.getYarnApplicationState();
                        return state == YarnApplicationState.RUNNING
                            || state == YarnApplicationState.SUBMITTED
                            || state == YarnApplicationState.ACCEPTED;
                    })
                    .collect(Collectors.toList());

                // 构建以applicationId为键的Map
                Map<String, ApplicationReport> activeAppMap = new HashMap<>();
                for (ApplicationReport app : activeApplications) {
                    activeAppMap.put(app.getApplicationId().toString(), app);
                }

                log.debug("成功获取Yarn活动应用列表，活动应用数量: {}", activeAppMap.size());
                return activeAppMap;

            } catch (Exception e) {
                log.error("批量获取Yarn活动应用状态时发生异常", e);
                if (e instanceof YarnException) {
                    throw new YarnException("Yarn集群批量查询失败: " + e.getMessage(), e);
                } else if (e instanceof IOException) {
                    throw new IOException("Yarn集群连接失败: " + e.getMessage(), e);
                } else {
                    throw new BizException("Yarn批量应用状态查询异常: " + e.getMessage(), e);
                }
            } finally {
                if (Objects.nonNull(yarnClient)) {
                    try {
                        yarnClient.close();
                    } catch (IOException e) {
                        log.warn("关闭YarnClient时发生异常", e);
                        // 不重新抛出异常，避免掩盖主要异常
                    }
                }
            }
        });
    }

    /**
     * 获取 Yarn 应用报告
     *
     * @param appId      应用ID
     * @param principal  Kerberos认证principal
     * @param keytabPath Kerberos认证keytab文件路径
     * @return ApplicationReport 应用报告，如果应用不存在则返回null
     */
    public ApplicationReport getYarnApplicationReport(String appId, String principal, String keytabPath)
        throws IOException, InterruptedException {
        if (Objects.isNull(appId) || appId.trim().isEmpty()) {
            log.warn("应用ID为空，无法查询Yarn应用状态");
            return null;
        }

        log.debug("开始查询Yarn应用状态 [appId:{}, authProvided:{}]", appId,
            Objects.nonNull(principal) && Objects.nonNull(keytabPath));

        // 使用RetryUtils执行带重试的Yarn应用查询
        return RetryUtils.executeWithDefaultRetry("Yarn应用状态查询[" + appId + "]", () -> {
            YarnClient yarnClient = null;
            try {
                // 使用KerberosAuthUtils创建Hadoop配置
                Configuration conf = KerberosAuthUtils.createHadoopConfiguration(hadoopProperties);

                // 使用KerberosAuthUtils执行Kerberos认证
                if (KerberosAuthUtils.isValidKerberosAuth(principal, keytabPath)) {
                    KerberosAuthUtils.authenticateWithKerberos(conf, principal, keytabPath);
                }

                // 创建YarnClient
                yarnClient = YarnClient.createYarnClient();
                yarnClient.init(conf);
                yarnClient.start();

                // 查询应用报告
                ApplicationReport report = yarnClient.getApplicationReport(ApplicationId.fromString(appId));
                log.debug("成功获取Yarn应用报告 [appId:{}, yarnState:{}]", appId,
                    report.getYarnApplicationState());
                return report;

            } catch (ApplicationNotFoundException e) {
                log.info("Yarn集群中未找到应用 [appId:{}]", appId);
                return null;
            } catch (Exception e) {
                log.error("查询Yarn应用状态时发生异常 [appId:{}]", appId, e);
                if (e instanceof YarnException) {
                    throw new YarnException("Yarn集群查询失败: " + e.getMessage(), e);
                } else if (e instanceof IOException) {
                    throw new IOException("Yarn集群连接失败: " + e.getMessage(), e);
                } else {
                    throw new BizException("Yarn应用状态查询异常: " + e.getMessage(), e);
                }
            } finally {
                if (Objects.nonNull(yarnClient)) {
                    try {
                        yarnClient.close();
                    } catch (IOException e) {
                        log.warn("关闭YarnClient时发生异常 [appId:{}]", appId, e);
                        // 不重新抛出异常，避免掩盖主要异常
                    }
                }
            }
        });
    }

    /**
     * 获取 Standalone 模式下的应用状态
     *
     * @param appId 应用ID
     * @return 应用状态枚举（如BatchTaskStatus.RUNNING、BatchTaskStatus.SUCCESS等），如果应用不存在则返回null
     */
    public BatchTaskStatus getStandaloneApplicationState(String appId)
        throws BizException, IOException, InterruptedException {
        if (Objects.isNull(appId) || appId.trim().isEmpty()) {
            log.warn("应用ID为空，无法查询Standalone应用状态");
            return null;
        }

        String webUiUrl = sparkApplicationConfig.getWebUiUrl();
        if (Objects.isNull(webUiUrl) || webUiUrl.trim().isEmpty()) {
            log.warn("Spark Web UI URL未配置，无法查询Standalone应用状态");
            throw new BizException("Spark Web UI URL未配置，请检查配置项spark.web-ui-url");
        }

        // 使用RetryUtils执行带重试的Standalone应用查询
        return RetryUtils.executeWithDefaultRetry("Standalone应用状态查询[" + appId + "]", () -> {
            String appUrl = String.format("%s/app/?appId=%s", webUiUrl.replaceAll("/$", ""), appId);
            log.debug("查询Standalone应用状态 [appId:{}, url:{}]", appId, appUrl);

            try {
                // 发起HTTP GET请求
                ResponseEntity<String> response = restTemplate.getForEntity(appUrl, String.class);

                if (response.getStatusCode() == HttpStatus.NOT_FOUND) {
                    log.info("Standalone集群中未找到应用 [appId:{}]", appId);
                    return null;
                }

                if (response.getStatusCode() != HttpStatus.OK) {
                    throw new BizException("HTTP请求失败，状态码: " + response.getStatusCode() +
                        ", URL: " + appUrl);
                }

                // 使用Jsoup解析HTML页面
                if (Objects.isNull(response.getBody())) {
                    throw new BizException("HTTP请求失败，响应体为空");
                }
                Document doc = Jsoup.parse(response.getBody());

                // 查找状态信息 - 查找包含"State:"的li元素
                Element stateElement = doc.selectFirst("li:contains(State:)");
                if (Objects.nonNull(stateElement)) {
                    // 获取状态值（去除"State:"前缀）
                    String stateText = stateElement.text().replaceFirst("State:\\s*", "").trim();
                    log.debug("解析到应用状态 [appId:{}, state:{}]", appId, stateText);
                    return BatchTaskStatus.fromStandaloneState(stateText);
                }

                // 如果没有找到状态信息，返回DISCARDED
                log.warn("无法从页面中解析应用状态 [appId:{}, url:{}]", appId, appUrl);
                return BatchTaskStatus.DISCARDED;

            } catch (HttpClientErrorException e) {
                if (e.getStatusCode() == HttpStatus.NOT_FOUND) {
                    log.info("Standalone集群中未找到应用 [appId:{}, url:{}]", appId, appUrl);
                    return null;
                }
                log.error("HTTP客户端异常 [appId:{}, url:{}, status:{}]", appId, appUrl, e.getStatusCode(), e);
                throw new BizException("Standalone集群HTTP请求失败: " + e.getMessage(), e);
            } catch (ResourceAccessException e) {
                log.error("网络连接异常 [appId:{}, url:{}]", appId, appUrl, e);
                throw new BizException("Standalone集群连接失败，请检查网络和Web UI配置: " + e.getMessage(), e);
            } catch (Exception e) {
                log.error("查询Standalone应用状态时发生异常 [appId:{}, url:{}]", appId, appUrl, e);
                throw new BizException("Standalone应用状态查询失败: " + e.getMessage(), e);
            }
        });
    }
}