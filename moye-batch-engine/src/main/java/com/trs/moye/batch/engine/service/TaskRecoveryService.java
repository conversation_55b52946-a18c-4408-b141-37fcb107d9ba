package com.trs.moye.batch.engine.service;

import com.trs.moye.base.data.connection.dao.AuthCertificateMapper;
import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.batch.engine.dao.clickhouse.BatchTaskRecordMapper;
import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import com.trs.moye.batch.engine.enums.BatchTaskStatus;
import com.trs.moye.batch.engine.spark.config.SparkApplicationConfig;
import com.trs.moye.batch.engine.utils.KerberosAuthUtils;
import com.trs.moye.batch.engine.utils.RetryUtils;
import com.trs.moye.batch.engine.utils.TaskRecoveryMonitor;
import com.trs.moye.batch.engine.utils.TaskStatusUpdateUtils;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.yarn.api.records.ApplicationReport;
import org.apache.hadoop.yarn.api.records.FinalApplicationStatus;
import org.apache.hadoop.yarn.api.records.YarnApplicationState;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * 任务恢复服务 - 应用启动时自愈流程的入口和执行者
 *
 * <AUTHOR>
 * @since 2025/08/30
 */
@Slf4j
@Service
public class TaskRecoveryService {

    @Resource
    private ClusterQueryService clusterQueryService;
    @Resource
    private BatchTaskRecordMapper batchTaskRecordMapper;
    @Resource
    private SparkApplicationConfig sparkApplicationConfig;
    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;
    @Resource
    private AuthCertificateMapper authCertificateMapper;

    /**
     * 应用启动时的任务恢复入口 检查并修正所有因服务中断而可能产生的"孤儿"任务的状态
     */
    @EventListener(ApplicationReadyEvent.class)
    public void recoverOrphanTasks() {
        log.info("=== 开始执行任务恢复流程 ===");

        // 启动恢复监控
        TaskRecoveryMonitor.startRecoveryMonitoring();

        int candidateCount = 0;
        int recoveredCount = 0;
        int errorCount = 0;

        try {
            // 查询候选任务：状态为SUBMITTING或RUNNING的任务
            List<BatchTaskStatus> candidateStatuses = Arrays.asList(
                BatchTaskStatus.SUBMITTING,
                BatchTaskStatus.RUNNING
            );
            List<BatchTaskRecord> candidateTasks = batchTaskRecordMapper.findByStatusIn(candidateStatuses);
            candidateCount = candidateTasks.size();

            if (candidateTasks.isEmpty()) {
                log.info("未找到需要恢复的任务，恢复流程结束");
                return;
            }

            log.info("找到 {} 个候选任务需要检查恢复状态", candidateCount);

            for (BatchTaskRecord task : candidateTasks) {
                // 记录候选任务
                TaskRecoveryMonitor.recordCandidateTask();

                try {
                    if (processTask(task)) {
                        recoveredCount++;
                    }
                } catch (Exception e) {
                    errorCount++;

                    log.error("处理任务 [executeId:{}] 时发生异常，继续处理其他任务", task.getExecuteId(), e);
                    // 继续处理其他任务，不让单个任务异常中断整个恢复流程

                    // 对于无法处理的任务，尝试标记为DISCARDED状态
                    try {
                        TaskStatusUpdateUtils.updateTaskStatus(task, BatchTaskStatus.DISCARDED,
                            String.format("任务恢复处理异常，由服务启动恢复机制标记为DISCARDED: %s", e.getMessage()),
                            batchTaskRecordMapper);
                        recoveredCount++; // 计入恢复数量
                    } catch (Exception updateException) {
                        log.error("更新异常任务状态时发生二次异常 [executeId:{}]", task.getExecuteId(),
                            updateException);
                    }
                }
            }

        } catch (Exception e) {
            log.error("执行任务恢复流程时发生严重异常", e);
        } finally {
            // 结束恢复监控
            TaskRecoveryMonitor.endRecoveryMonitoring();

            // 记录恢复完成统计信息
            TaskRecoveryMonitor.recordRecoveryCompletion(candidateCount, recoveredCount, errorCount);

            log.info("=== 任务恢复流程完成 === 候选任务: {}, 成功恢复: {}, 处理异常: {}",
                candidateCount, recoveredCount, errorCount);
            log.info("恢复统计: {}", TaskRecoveryMonitor.getRecoveryStatistics());
        }
    }

    /**
     * 处理单个任务的状态恢复
     *
     * @param task 任务记录
     * @return 是否进行了状态恢复
     */
    private boolean processTask(BatchTaskRecord task) {
        String executeId = task.getExecuteId();
        String applicationId = task.getApplicationId();

        log.debug("开始处理任务 [executeId:{}, applicationId:{}, status:{}]",
            executeId, applicationId, task.getStatus());

        // 如果没有applicationId，直接标记为FAILED
        if (Objects.isNull(applicationId) || applicationId.trim().isEmpty()) {
            log.info("任务 [executeId:{}] 没有applicationId，标记为FAILED", executeId);
            TaskStatusUpdateUtils.updateTaskStatus(task, BatchTaskStatus.FAILED,
                "任务没有applicationId，由服务启动恢复机制标记为FAILED", batchTaskRecordMapper);
            return true;
        }

        // 根据部署模式查询应用状态，使用RetryUtils执行带重试的任务处理
        try {
            return RetryUtils.executeWithDefaultRetry("任务处理[" + executeId + "]", () -> {
                if (sparkApplicationConfig.getMode().isYarnMode()) {
                    return processYarnTask(task);
                } else {
                    return processStandaloneTask(task);
                }
            });
        } catch (Exception e) {
            log.error("查询任务 [executeId:{}, applicationId:{}] 集群状态时发生异常",
                executeId, applicationId, e);
            // 查询异常时标记为DISCARDED
            TaskStatusUpdateUtils.updateTaskStatus(task, BatchTaskStatus.DISCARDED,
                String.format("集群状态查询异常，由服务启动恢复机制标记为DISCARDED: %s", e.getMessage()),
                batchTaskRecordMapper);
            return true;
        }
    }

    /**
     * 处理Yarn模式下的任务
     *
     * @param task 任务记录
     * @return 是否进行了状态恢复
     */
    private boolean processYarnTask(BatchTaskRecord task) throws Exception {
        String executeId = task.getExecuteId();
        String applicationId = task.getApplicationId();

        // 使用RetryUtils执行带重试的Yarn任务处理
        return RetryUtils.executeWithDefaultRetry("Yarn任务处理[" + executeId + "]", () -> {
            // 提前查询数据库获取执行配置，避免多次查询
            Integer dataModelId = Integer.valueOf(task.getTaskId());
            DataModelExecuteConfig executeConfig = dataModelExecuteConfigMapper.selectByDataModelId(dataModelId);

            // 使用KerberosAuthUtils从已查询的执行配置中提取Kerberos证书
            KerberosCertificate certificate = KerberosAuthUtils.extractKerberosCertificateFromConfig(
                executeConfig, authCertificateMapper);

            String principal = certificate != null ? certificate.getPrincipal() : null;
            String keytabPath = certificate != null ? certificate.getKeytabPath() : null;

            log.debug("开始处理Yarn任务 [executeId:{}, applicationId:{}, authProvided:{}]",
                executeId, applicationId, KerberosAuthUtils.isValidKerberosAuth(principal, keytabPath));

            // 查询Yarn应用报告，支持Kerberos认证
            ApplicationReport report = clusterQueryService.getYarnApplicationReport(
                applicationId, principal, keytabPath);

            if (Objects.isNull(report)) {
                // 应用未找到，标记为DISCARDED
                log.info("Yarn集群中未找到任务 [executeId:{}, applicationId:{}]，标记为DISCARDED",
                    executeId, applicationId);
                TaskStatusUpdateUtils.updateTaskStatus(task, BatchTaskStatus.DISCARDED,
                    "应用在Yarn集群中未找到，由服务启动恢复机制标记为DISCARDED", batchTaskRecordMapper);
                return true;
            }

            // 根据Yarn应用状态映射任务状态，同时考虑最终应用状态
            YarnApplicationState yarnState = report.getYarnApplicationState();
            FinalApplicationStatus finalStatus = report.getFinalApplicationStatus();
            BatchTaskStatus targetStatus = BatchTaskStatus.fromYarnState(yarnState, finalStatus);

            if (targetStatus != null && !targetStatus.equals(task.getStatus())) {
                log.info("任务 [executeId:{}, applicationId:{}] Yarn状态为 {}，更新任务状态为 {}",
                    executeId, applicationId, yarnState, targetStatus);
                TaskStatusUpdateUtils.updateTaskStatus(task, targetStatus,
                    String.format("根据Yarn集群状态[%s]由服务启动恢复机制更新状态为%s", yarnState, targetStatus),
                    batchTaskRecordMapper);
                return true;
            }

            // 如果是活动状态但任务记录显示RUNNING/SUBMITTING，判定为"孤儿"任务
            if (isActiveYarnState(yarnState) && (BatchTaskStatus.RUNNING.equals(task.getStatus())
                || BatchTaskStatus.SUBMITTING.equals(task.getStatus()))) {
                log.info("任务 [executeId:{}, applicationId:{}] 为孤儿任务，Yarn状态为活动状态 {}，标记为DISCARDED",
                    executeId, applicationId, yarnState);
                TaskStatusUpdateUtils.updateTaskStatus(task, BatchTaskStatus.DISCARDED,
                    String.format("检测到孤儿任务，Yarn状态为活动状态[%s]但服务已重启，由服务启动恢复机制标记为DISCARDED",
                        yarnState), batchTaskRecordMapper);
                return true;
            }

            return false;
        });
    }


    /**
     * 处理Standalone模式下的任务
     *
     * @param task 任务记录
     * @return 是否进行了状态恢复
     */
    private boolean processStandaloneTask(BatchTaskRecord task) throws Exception {
        String executeId = task.getExecuteId();
        String applicationId = task.getApplicationId();

        log.debug("开始处理Standalone任务 [executeId:{}, applicationId:{}]", executeId, applicationId);

        // 使用RetryUtils执行带重试的Standalone任务处理
        return RetryUtils.executeWithDefaultRetry("Standalone任务处理[" + executeId + "]", () -> {
            // 查询Standalone应用状态
            BatchTaskStatus appState = clusterQueryService.getStandaloneApplicationState(applicationId);

            if (Objects.isNull(appState)) {
                // 应用未找到，标记为DISCARDED
                log.info("Standalone集群中未找到任务 [executeId:{}, applicationId:{}]，标记为DISCARDED",
                    executeId, applicationId);
                TaskStatusUpdateUtils.updateTaskStatus(task, BatchTaskStatus.DISCARDED,
                    "应用在Standalone集群中未找到，由服务启动恢复机制标记为DISCARDED", batchTaskRecordMapper);
                return true;
            }

            // 直接使用返回的枚举状态，无需再映射

            if (!appState.equals(task.getStatus())) {
                log.info("任务 [executeId:{}, applicationId:{}] Standalone状态为 {}，更新任务状态为 {}",
                    executeId, applicationId, appState, appState);
                TaskStatusUpdateUtils.updateTaskStatus(task, appState,
                    String.format("根据Standalone集群状态[%s]由服务启动恢复机制更新状态为%s", appState, appState),
                    batchTaskRecordMapper);
                return true;
            }

            // 如果是RUNNING状态但任务记录显示RUNNING/SUBMITTING，判定为"孤儿"任务
            if (BatchTaskStatus.RUNNING.equals(appState)) {
                log.info("任务 [executeId:{}, applicationId:{}] 为孤儿任务，Standalone状态为RUNNING，标记为DISCARDED",
                    executeId, applicationId);
                TaskStatusUpdateUtils.updateTaskStatus(task, BatchTaskStatus.DISCARDED,
                    "检测到孤儿任务，Standalone状态为RUNNING但服务已重启，由服务启动恢复机制标记为DISCARDED",
                    batchTaskRecordMapper);
                return true;
            }

            // 状态一致，无需恢复
            log.debug("任务 [executeId:{}, applicationId:{}] Standalone状态 {} 与当前状态一致，无需恢复",
                executeId, applicationId, appState);
            return false;
        });
    }

    /**
     * 判断是否为活动的Yarn状态
     *
     * @param yarnState Yarn应用状态
     * @return 是否为活动状态
     */
    private boolean isActiveYarnState(YarnApplicationState yarnState) {
        return yarnState == YarnApplicationState.RUNNING || yarnState == YarnApplicationState.SUBMITTED
            || yarnState == YarnApplicationState.ACCEPTED;
    }

}