package com.trs.moye.batch.engine.utils;


import com.trs.moye.base.common.exception.BizException;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * 重试工具类 - 提供统一的重试机制
 *
 * <AUTHOR>
 * @since 2025/08/31
 */
@Slf4j
public class RetryUtils {

    /**
     * 重试接口
     *
     * @param <T> 返回类型
     */
    @FunctionalInterface
    public interface RetryableTask<T> {

        /**
         * 执行
         *
         * @return {@link T }
         * @throws Exception 异常
         * <AUTHOR>
         * @since 2025/08/31 15:48:17
         */
        T execute() throws Exception;
    }

    /**
     * 可重试的异常类型
     */
    private static final List<Class<? extends Exception>> RETRYABLE_EXCEPTIONS = Arrays.asList(
        java.net.ConnectException.class,
        java.net.SocketTimeoutException.class,
        java.io.IOException.class,
        org.apache.hadoop.yarn.exceptions.YarnException.class,
        org.springframework.web.client.ResourceAccessException.class
    );

    /**
     * 判断异常是否可重试
     *
     * @param exception 异常
     * @return 是否可重试
     */
    private static boolean isRetryableException(Exception exception) {
        if (exception == null) {
            return false;
        }

        // 检查异常本身
        for (Class<? extends Exception> retryableException : RETRYABLE_EXCEPTIONS) {
            if (retryableException.isInstance(exception)) {
                return true;
            }
        }

        // 检查原因异常
        Throwable cause = exception.getCause();
        while (cause != null) {
            for (Class<? extends Exception> retryableException : RETRYABLE_EXCEPTIONS) {
                if (retryableException.isInstance(cause)) {
                    return true;
                }
            }
            cause = cause.getCause();
        }

        return false;
    }

    /**
     * 处理线程中断异常
     *
     * @param taskName 任务名称
     * @param ie       中断异常
     * @throws BizException 业务异常
     */
    private static void handleInterruptedException(String taskName, InterruptedException ie) {
        Thread.currentThread().interrupt();
        throw new BizException(taskName + " 被中断", ie);
    }

    /**
     * 根据异常类型抛出具体异常
     *
     * @param taskName    任务名称
     * @param e           异常
     * @param isRetryable 是否可重试
     * @param maxRetries  最大重试次数
     * @throws RuntimeException     运行时异常
     * @throws IOException          IO异常
     * @throws InterruptedException 中断异常
     * @throws BizException         业务异常
     */
    private static void throwSpecificException(String taskName, Exception e, boolean isRetryable, int maxRetries)
        throws IOException, InterruptedException {
        if (!isRetryable) {
            log.error("任务 {} 遇到不可重试异常，直接抛出 [exception:{}]", taskName,
                e.getClass().getSimpleName());
            // 如果是具体的异常类型，直接抛出
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            } else if (e instanceof java.io.IOException) {
                throw (java.io.IOException) e;
            } else if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                throw (InterruptedException) e;
            } else {
                // 其他异常类型包装为BizException
                throw new BizException(taskName + " 执行失败: " + e.getMessage(), e);
            }
        } else {
            log.error("任务 {} 重试次数耗尽 [maxRetries:{}]", taskName, maxRetries, e);
            // 重试次数耗尽，抛出BizException
            throw new BizException(taskName + " 重试次数耗尽: " + e.getMessage(), e);
        }
    }

    /**
     * 通用的重试执行逻辑
     *
     * @param taskName             任务名称
     * @param task                 重试任务
     * @param maxRetries           最大重试次数
     * @param retryDelayCalculator 重试延迟计算器
     * @param retryOnAllExceptions 是否在所有异常上重试
     * @param <T>                  返回类型
     * @return 执行结果
     * @throws BizException 业务异常
     */
    private static <T> T executeWithRetryInternal(String taskName, RetryableTask<T> task,
        int maxRetries, RetryDelayCalculator retryDelayCalculator, boolean retryOnAllExceptions)
        throws IOException, InterruptedException {
        int retryCount = 0;

        while (retryCount <= maxRetries) {
            try {
                T result = task.execute();
                if (retryCount > 0) {
                    log.info("任务 {} 在第{}次重试后成功执行", taskName, retryCount);
                }
                return result;

            } catch (Exception e) {
                retryCount++;

                // 检查是否应该重试
                boolean shouldRetry = retryOnAllExceptions || isRetryableException(e);

                if (retryCount <= maxRetries && shouldRetry) {
                    long delay = retryDelayCalculator.calculateDelay(retryCount);
                    log.warn("任务 {} 执行失败，第{}次重试，延迟{}ms [maxRetries:{}] - {}",
                        taskName, retryCount, delay, maxRetries, e.getMessage());
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        handleInterruptedException(taskName, ie);
                    }
                } else {
                    throwSpecificException(taskName, e, shouldRetry, maxRetries);
                }
            }
        }

        throw new BizException(taskName + " 重试机制异常");
    }

    /**
     * 重试延迟计算器接口
     */
    @FunctionalInterface
    private interface RetryDelayCalculator {

        /**
         * 计算重试延迟
         *
         * @param retryCount 重试次数
         * @return 延迟时间（毫秒）
         */
        long calculateDelay(int retryCount);
    }

    /**
     * 带重试的任务执行
     *
     * @param taskName   任务名称
     * @param task       重试任务
     * @param maxRetries 最大重试次数
     * @param retryDelay 重试间隔（毫秒）
     * @param <T>        返回类型
     * @return 执行结果
     * @throws BizException 业务异常
     */
    public static <T> T executeWithRetry(String taskName, RetryableTask<T> task,
        int maxRetries, long retryDelay) throws IOException, InterruptedException {
        return executeWithRetryInternal(taskName, task, maxRetries,
            retryCount -> retryDelay * retryCount, true);
    }

    /**
     * 带重试的任务执行（增强版）
     *
     * @param taskName             任务名称
     * @param task                 重试任务
     * @param maxRetries           最大重试次数
     * @param retryDelay           重试间隔（毫秒）
     * @param retryOnAllExceptions 是否在所有异常上重试，false则只在可重试异常上重试
     * @param <T>                  返回类型
     * @return 执行结果
     * @throws BizException 业务异常
     */
    public static <T> T executeWithRetry(String taskName, RetryableTask<T> task,
        int maxRetries, long retryDelay, boolean retryOnAllExceptions) throws IOException, InterruptedException {
        return executeWithRetryInternal(taskName, task, maxRetries,
            retryCount -> retryDelay * retryCount, retryOnAllExceptions);
    }

    /**
     * 默认重试策略的任务执行（3次重试，1秒间隔）
     *
     * @param taskName 任务名称
     * @param task     重试任务
     * @param <T>      返回类型
     * @return 执行结果
     * @throws BizException 业务异常
     */
    public static <T> T executeWithDefaultRetry(String taskName, RetryableTask<T> task)
        throws IOException, InterruptedException {
        return executeWithRetry(taskName, task, 3, 1000, false);
    }

    /**
     * 快速重试策略（适用于网络抖动等临时性问题）
     *
     * @param taskName 任务名称
     * @param task     重试任务
     * @param <T>      返回类型
     * @return 执行结果
     * @throws BizException 业务异常
     */
    public static <T> T executeWithFastRetry(String taskName, RetryableTask<T> task)
        throws IOException, InterruptedException {
        return executeWithRetry(taskName, task, 5, 500, false);
    }

    /**
     * 慢速重试策略（适用于资源紧张等需要等待的场景）
     *
     * @param taskName 任务名称
     * @param task     重试任务
     * @param <T>      返回类型
     * @return 执行结果
     * @throws BizException 业务异常
     */
    public static <T> T executeWithSlowRetry(String taskName, RetryableTask<T> task)
        throws IOException, InterruptedException {
        return executeWithRetry(taskName, task, 3, 3000, false);
    }

    /**
     * 带退避策略的重试执行
     *
     * @param taskName     任务名称
     * @param task         重试任务
     * @param maxRetries   最大重试次数
     * @param initialDelay 初始重试间隔（毫秒）
     * @param maxDelay     最大重试间隔（毫秒）
     * @param <T>          返回类型
     * @return 执行结果
     * @throws BizException 业务异常
     */
    public static <T> T executeWithBackoffRetry(String taskName, RetryableTask<T> task,
        int maxRetries, long initialDelay, long maxDelay) throws IOException, InterruptedException {
        return executeWithBackoffRetry(taskName, task, maxRetries, initialDelay, maxDelay, false);
    }

    /**
     * 带退避策略的重试执行（增强版）
     *
     * @param taskName             任务名称
     * @param task                 重试任务
     * @param maxRetries           最大重试次数
     * @param initialDelay         初始重试间隔（毫秒）
     * @param maxDelay             最大重试间隔（毫秒）
     * @param retryOnAllExceptions 是否在所有异常上重试
     * @param <T>                  返回类型
     * @return 执行结果
     * @throws BizException 业务异常
     */
    public static <T> T executeWithBackoffRetry(String taskName, RetryableTask<T> task,
        int maxRetries, long initialDelay, long maxDelay,
        boolean retryOnAllExceptions) throws IOException, InterruptedException {
        return executeWithRetryInternal(taskName, task, maxRetries,
            retryCount -> Math.min(initialDelay * (1L << (retryCount - 1)), maxDelay), retryOnAllExceptions);
    }

    /**
     * 默认退避重试策略（3次重试，指数退避，最大5秒）
     *
     * @param taskName 任务名称
     * @param task     重试任务
     * @param <T>      返回类型
     * @return 执行结果
     * @throws BizException 业务异常
     */
    public static <T> T executeWithDefaultBackoffRetry(String taskName, RetryableTask<T> task)
        throws IOException, InterruptedException {
        return executeWithBackoffRetry(taskName, task, 3, 1000, 5000, false);
    }
}