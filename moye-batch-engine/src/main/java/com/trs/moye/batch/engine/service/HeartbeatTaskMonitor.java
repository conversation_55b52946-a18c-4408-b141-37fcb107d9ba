package com.trs.moye.batch.engine.service;

import com.trs.ai.ty.base.entity.exception.BizException;
import com.trs.moye.base.data.connection.dao.AuthCertificateMapper;
import com.trs.moye.base.data.connection.entity.KerberosCertificate;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.batch.engine.dao.clickhouse.BatchTaskRecordMapper;
import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import com.trs.moye.batch.engine.enums.BatchTaskStatus;
import com.trs.moye.batch.engine.spark.base.Mode;
import com.trs.moye.batch.engine.spark.config.SparkApplicationConfig;
import com.trs.moye.batch.engine.utils.KerberosAuthUtils;
import com.trs.moye.batch.engine.utils.RetryUtils;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.yarn.api.records.ApplicationReport;
import org.apache.hadoop.yarn.api.records.FinalApplicationStatus;
import org.apache.hadoop.yarn.api.records.YarnApplicationState;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * 心跳任务监控服务 - 主动式状态监控的核心实现 该服务通过内置的、隔离的单线程执行器模拟"心跳"，以固定的周期轮询所有正在运行的任务 确保每个批处理任务在提交后都能得到持续监听，并及时同步其最终状态
 *
 * <AUTHOR>
 * @since 2025/09/02 14:43:49
 */
@Slf4j
@Service
public class HeartbeatTaskMonitor {

    @Value("${moye.batch.engine.heartbeat-monitor.interval:60000}")
    private long monitorInterval;

    @Value("${moye.batch.engine.heartbeat-monitor.parallelism:3}")
    private int parallelism;

    @Resource
    private BatchTaskRecordMapper batchTaskRecordMapper;

    @Resource
    private ClusterQueryService clusterQueryService;

    @Resource
    private PostCompletionService postCompletionService;

    @Resource
    private SparkApplicationConfig sparkApplicationConfig;

    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;

    @Resource
    private AuthCertificateMapper authCertificateMapper;

    // 隔离的单线程心跳执行器，确保监控任务串行执行，避免请求风暴
    private final ScheduledExecutorService monitorExecutor = Executors.newSingleThreadScheduledExecutor(
        runnable -> new Thread(runnable, "HeartbeatTaskMonitorThread")
    );

    // 用于并行处理任务的执行器
    private final java.util.concurrent.ExecutorService taskProcessingExecutor =
        Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

    /**
     * 应用启动时开始心跳监控
     */
    @EventListener(ApplicationReadyEvent.class)
    public void start() {
        log.info("启动批处理任务心跳监控，监控周期: {} ms, 并行度: {}", monitorInterval, parallelism);
        monitorExecutor.scheduleAtFixedRate(
            this::runMonitorCycle,
            15, // 初始延迟15秒，确保应用完全启动
            monitorInterval,
            TimeUnit.MILLISECONDS
        );
    }

    /**
     * 应用关闭时停止心跳监控
     */
    @PreDestroy
    public void stop() {
        log.info("开始停止批处理任务心跳监控...");
        monitorExecutor.shutdown();
        taskProcessingExecutor.shutdown();
        try {
            if (!monitorExecutor.awaitTermination(15, TimeUnit.SECONDS)) {
                monitorExecutor.shutdownNow();
            }
            if (!taskProcessingExecutor.awaitTermination(15, TimeUnit.SECONDS)) {
                taskProcessingExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            monitorExecutor.shutdownNow();
            taskProcessingExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("批处理任务心跳监控已成功停止。");
    }

    /**
     * 执行监控周期的主要逻辑
     */
    private void runMonitorCycle() {
        try {
            log.debug("开始执行心跳监控周期");

            // 1. 从数据库获取所有 RUNNING 状态的任务
            List<BatchTaskStatus> runningStatuses = List.of(BatchTaskStatus.RUNNING);
            List<BatchTaskRecord> runningTasks = batchTaskRecordMapper.findByStatusIn(runningStatuses);

            if (runningTasks.isEmpty()) {
                log.debug("当前没有正在运行的任务，跳过本次监控周期");
                return;
            }

            log.debug("找到 {} 个正在运行的任务需要监控", runningTasks.size());

            // 2. 根据部署模式进行批量或单个查询
            if (sparkApplicationConfig.getMode().isYarnMode()) {
                // Yarn模式：批量获取集群状态
                processYarnTasks(runningTasks);
            } else {
                // Standalone模式：并行查询和处理
                processStandaloneTasksInParallel(runningTasks);
            }

            log.debug("心跳监控周期执行完成");

        } catch (Exception e) {
            log.error("心跳监控周期执行时发生顶层异常，已捕获，不影响下次执行。", e);
        }
    }

    /**
     * 处理Yarn模式下的任务
     *
     * @param runningTasks 正在运行的任务列表
     * <AUTHOR>
     * @since 2025/09/02 11:37:08
     */
    private void processYarnTasks(List<BatchTaskRecord> runningTasks) throws Exception {
        log.debug("开始处理Yarn模式下的 {} 个任务", runningTasks.size());

        // 使用RetryUtils执行带重试的批量任务处理
        RetryUtils.executeWithDefaultRetry("Yarn批量任务处理", () -> {
            // 1. 预先提取所有任务的认证信息并按认证分组
            Map<KerberosCertificate, List<BatchTaskRecord>> tasksByAuth = groupTasksByAuthentication(runningTasks);

            // 2. 处理每个认证组
            for (Map.Entry<KerberosCertificate, List<BatchTaskRecord>> entry : tasksByAuth.entrySet()) {
                KerberosCertificate certificate = entry.getKey();
                List<BatchTaskRecord> authGroupTasks = entry.getValue();

                if (Objects.isNull(certificate)) {
                    // 没有认证信息的任务组
                    processTasksWithoutAuthentication(authGroupTasks);
                } else {
                    // 有认证信息的任务组
                    processTasksWithAuthentication(authGroupTasks, certificate.getPrincipal(),
                        certificate.getKeytabPath());
                }
            }

            return null;
        });
    }

    /**
     * 按认证信息对任务进行分组
     *
     * @param tasks 任务列表
     * @return 按认证信息分组的任务映射
     */
    private Map<KerberosCertificate, List<BatchTaskRecord>> groupTasksByAuthentication(List<BatchTaskRecord> tasks) {
        // 直接使用KerberosCertificate对象作为分组键
        return tasks.stream().collect(
            Collectors.groupingBy(this::extractKerberosCertificate, ConcurrentHashMap::new, Collectors.toList()));
    }

    /**
     * 处理没有认证信息的任务组
     *
     * @param tasks 任务列表
     */
    private void processTasksWithoutAuthentication(List<BatchTaskRecord> tasks)
        throws IOException, InterruptedException {
        // 批量获取Yarn活动应用状态（不使用认证）
        Map<String, ApplicationReport> clusterApps = clusterQueryService.getYarnActiveApplications(null, null);
        log.debug("从Yarn集群获取到 {} 个活动应用（无认证）", clusterApps.size());

        // 处理每个任务
        for (BatchTaskRecord task : tasks) {
            processYarnTaskWithClusterInfo(task, clusterApps);
        }
    }

    /**
     * 处理有认证信息的任务组
     *
     * @param tasks      任务列表
     * @param principal  Kerberos认证principal
     * @param keytabPath Kerberos认证keytab文件路径
     */
    private void processTasksWithAuthentication(List<BatchTaskRecord> tasks, String principal, String keytabPath)
        throws IOException, InterruptedException {
        // 批量获取Yarn活动应用状态
        Map<String, ApplicationReport> clusterApps = clusterQueryService.getYarnActiveApplications(principal,
            keytabPath);
        log.debug("从Yarn集群获取到 {} 个活动应用（认证: {}）", clusterApps.size(), principal);

        // 处理每个任务
        for (BatchTaskRecord task : tasks) {
            processYarnTaskWithClusterInfo(task, clusterApps);
        }
    }

    /**
     * 处理单个Yarn任务（使用批量获取的集群状态）
     *
     * @param task        任务记录
     * @param clusterApps 集群应用状态映射
     */
    private void processYarnTaskWithClusterInfo(BatchTaskRecord task, Map<String, ApplicationReport> clusterApps) {
        String applicationId = task.getApplicationId();

        if (Objects.isNull(applicationId) || applicationId.trim().isEmpty()) {
            handleTaskWithoutApplicationId(task, Mode.YARN);
            return;
        }

        ApplicationReport report = clusterApps.get(applicationId);

        if (Objects.isNull(report)) {
            handleOrphanTask(task, Mode.YARN, applicationId);
            return;
        }

        // 根据Yarn应用状态映射任务状态，同时考虑最终应用状态
        YarnApplicationState yarnState = report.getYarnApplicationState();
        FinalApplicationStatus finalStatus = report.getFinalApplicationStatus();
        BatchTaskStatus targetStatus = BatchTaskStatus.fromYarnState(yarnState, finalStatus);

        handleTaskStatusUpdate(task, targetStatus, Mode.YARN, yarnState.toString());
    }

    /**
     * 并行处理Standalone模式下的任务
     *
     * @param runningTasks 正在运行的任务列表
     */
    private void processStandaloneTasksInParallel(List<BatchTaskRecord> runningTasks) {
        log.debug("开始并行处理Standalone模式下的 {} 个任务", runningTasks.size());

        // 使用并行流处理任务，控制并行度
        runningTasks.parallelStream()
            .limit(parallelism)
            .forEach(task -> {
                try {
                    processSingleStandaloneTask(task);
                } catch (Exception e) {
                    log.error("处理Standalone任务 [executeId:{}] 时发生异常", task.getExecuteId(), e);
                    // 单个任务异常不影响其他任务处理
                }
            });
    }

    /**
     * 处理单个Standalone任务
     *
     * @param task 任务记录
     */
    private void processSingleStandaloneTask(BatchTaskRecord task)
        throws BizException, IOException, InterruptedException {
        String applicationId = task.getApplicationId();

        if (Objects.isNull(applicationId) || applicationId.trim().isEmpty()) {
            handleTaskWithoutApplicationId(task, Mode.STANDALONE);
            return;
        }

        // 查询Standalone应用状态
        BatchTaskStatus appState = clusterQueryService.getStandaloneApplicationState(applicationId);

        if (Objects.isNull(appState)) {
            handleOrphanTask(task, Mode.STANDALONE, applicationId);
            return;
        }

        handleTaskStatusUpdate(task, appState, Mode.STANDALONE, appState.toString());
    }

    /**
     * 处理没有applicationId的任务
     *
     * @param task 任务记录
     * @param mode 部署模式（Yarn或Standalone）
     */
    private void handleTaskWithoutApplicationId(BatchTaskRecord task, Mode mode) {
        String executeId = task.getExecuteId();
        log.warn("任务 [executeId:{}] 没有applicationId，标记为FAILED", executeId);
        postCompletionService.finalizeTask(task, BatchTaskStatus.FAILED,
            String.format("任务没有applicationId，由%s心跳监控机制标记为FAILED", mode));
    }

    /**
     * 处理孤儿任务（在集群中未找到的任务）
     *
     * @param task          任务记录
     * @param mode          部署模式（Yarn或Standalone）
     * @param applicationId 应用ID
     */
    private void handleOrphanTask(BatchTaskRecord task, Mode mode, String applicationId) {
        String executeId = task.getExecuteId();
        log.info("任务 [executeId:{}, applicationId:{}] 在{}集群中未找到，判定为孤儿任务",
            executeId, applicationId, mode);
        postCompletionService.finalizeTask(task, BatchTaskStatus.DISCARDED,
            String.format("检测到孤儿任务，应用在%s集群中未找到，由心跳监控机制标记为DISCARDED", mode));
    }

    /**
     * 处理任务状态更新
     *
     * @param task         任务记录
     * @param targetStatus 目标状态
     * @param mode         部署模式（Yarn或Standalone）
     * @param stateStr     状态字符串
     */
    private void handleTaskStatusUpdate(BatchTaskRecord task, BatchTaskStatus targetStatus, Mode mode,
        String stateStr) {
        String executeId = task.getExecuteId();
        String applicationId = task.getApplicationId();

        if (targetStatus != null && !targetStatus.equals(task.getStatus())) {
            // 情况A：任务已结束
            log.info("任务 [executeId:{}, applicationId:{}] {}状态为 {}，更新任务状态为 {}",
                executeId, applicationId, mode, stateStr, targetStatus);
            postCompletionService.finalizeTask(task, targetStatus,
                String.format("根据%s集群状态[%s]由心跳监控机制更新状态为%s", mode, stateStr, targetStatus));
            return;
        }

        // 情况B：任务仍在运行 - 无需处理
        log.debug("任务 [executeId:{}, applicationId:{}] 仍在运行，状态为 {}", executeId, applicationId, stateStr);
    }

    /**
     * 从任务配置中提取Kerberos认证信息 复用TaskRecoveryService中的逻辑
     *
     * @param task 任务记录
     * @return Kerberos证书
     */
    private KerberosCertificate extractKerberosCertificate(BatchTaskRecord task) {
        try {
            Integer dataModelId = Integer.valueOf(task.getTaskId());
            DataModelExecuteConfig executeConfig = dataModelExecuteConfigMapper.selectByDataModelId(dataModelId);
            return KerberosAuthUtils.extractKerberosCertificateFromConfig(executeConfig, authCertificateMapper);
        } catch (Exception e) {
            log.error("提取任务 [executeId:{}] Kerberos认证信息时发生异常", task.getExecuteId(), e);
            return null;
        }
    }
}