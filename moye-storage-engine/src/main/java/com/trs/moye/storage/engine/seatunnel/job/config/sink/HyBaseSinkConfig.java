package com.trs.moye.storage.engine.seatunnel.job.config.sink;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.trs.moye.base.data.model.enums.DataSaveMode;
import com.trs.moye.storage.engine.seatunnel.enums.SchemaSaveMode;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 海贝存储配置
 *
 * <AUTHOR>
 * @since 2024/10/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class HyBaseSinkConfig extends SeatunnelSinkConfig {

    @JsonProperty("plugin_name")
    @Builder.Default
    private String pluginName = "Hybase";

    @JsonProperty("hosts")
    private String[] hosts;

    @JsonProperty("database")
    private String database;

    @JsonProperty("username")
    private String username;

    @JsonProperty("password")
    private String password;

    @JsonProperty("data_save_mode")
    private DataSaveMode dataSaveMode;

    @JsonProperty("schema_save_mode")
    private SchemaSaveMode schemaSaveMode;

    @JsonProperty("storage_name")
    private String storageName;
}
