package com.trs.moye.storage.engine.seatunnel.job.config.strategy;

import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.entity.params.RocketMqConnectionParams;
import com.trs.moye.base.data.execute.RocketMQExecuteParams;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.source.enums.RocketMqMessageFormat;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.IncrementInfo;
import com.trs.moye.storage.engine.pojo.request.task.TaskStartParam;
import com.trs.moye.storage.engine.seatunnel.job.config.schema.Schema;
import com.trs.moye.storage.engine.seatunnel.job.config.sink.RocketMqSinkConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.source.RocketMqSourceConfig;
import com.trs.moye.storage.engine.seatunnel.job.config.source.RocketMqSourceConfig.RocketMqSourceConfigBuilder;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;

/**
 * RocketMQ 数据源配置
 */
public class RocketmqSeatunnelJobConfigStrategy implements SeatunnelJobConfigStrategy {

    @Override
    public RocketMqSourceConfig createSourceConfig(DataSourceConfig dataSourceConfig, String tableName,
        List<DataModelField> fields, IncrementInfo incrementInfo, TaskStartParam taskStartParam) {
        RocketMqConnectionParams connectionParams = (RocketMqConnectionParams) dataSourceConfig.getConnection()
            .getConnectionParams();
        RocketMQExecuteParams rocketMqExecuteParams = (RocketMQExecuteParams) taskStartParam.getExecuteParams();
        String defaultGroup = connectionParams.buildConsumerGroup(fields.get(0).getDataModelId());
        String consumerGroup = ObjectUtils.isEmpty(rocketMqExecuteParams.getConsumerGroup())
            ? defaultGroup : rocketMqExecuteParams.getConsumerGroup();
        RocketMqSourceConfigBuilder<?, ?> builder = RocketMqSourceConfig.builder()
            .consumerGroup(consumerGroup)
            .nameServerAddr(connectionParams.getHost() + ":" + connectionParams.getPort())
            .topic(dataSourceConfig.getEnName()).schema(Schema.of(fields))
            .startMode(rocketMqExecuteParams.getOffsetResetType())
            .startModeTimestamp(rocketMqExecuteParams.getTimestamp());
        //是否消费wx格式数据
        if (RocketMqMessageFormat.WX.equals(connectionParams.getFormat())) {
            builder.format("wx_json");
        } else {
            builder.format("json");
        }
        // 是否开启acl认证
        if (StringUtils.isNotBlank(connectionParams.getUsername()) && StringUtils.isNotBlank(
            connectionParams.getPassword())) {
            builder.aclEnabled(true);
            builder.accessKey(connectionParams.getUsername());
            builder.secretKey(connectionParams.getPassword());
        }
        return builder.build();
    }

    @Override
    public RocketMqSinkConfig createSinkConfig(DataConnection connection, DataStorage storage,
        IncrementInfo incrementInfo, List<DataModelField> fields) {
        RocketMqConnectionParams connectionParams = (RocketMqConnectionParams) connection.getConnectionParams();
        RocketMqSinkConfig.RocketMqSinkConfigBuilder<?, ?> builder = RocketMqSinkConfig.builder()
            .topic(storage.getEnName())
            .storageName(storage.getConnection().getName())
            .nameServerAddr(connectionParams.getHost() + ":" + connectionParams.getPort());
        if (StringUtils.isNotBlank(connectionParams.getUsername()) && StringUtils.isNotBlank(
            connectionParams.getPassword())) {
            builder.aclEnabled(true);
            builder.accessKey(connectionParams.getUsername());
            builder.secretKey(connectionParams.getPassword());
        }
        return builder.build();
    }
}
