package com.trs.moye.storage.engine.seatunnel.job.config.schema;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.moye.base.common.entity.field.DecimalAdvanceConfig;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.storage.engine.seatunnel.enums.SeaTunnelDataType;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 列
 *
 * <p>列用于描述表的列信息</p>
 *
 * <AUTHOR>
 */
@Data
@Validated
public class Column {

    /**
     * 列名称
     */
    @NotBlank(message = "列名称不能为空")
    private String name;
    /**
     * 列类型
     * <ul>
     * <li>string</li>
     * <li>boolean</li>
     * <li>tinyint</li>
     * <li>smallint</li>
     * <li>int</li>
     * <li>bigint</li>
     * <li>float</li>
     * <li>double</li>
     * <li>decimal exp: decimal(10,2)</li>
     * <li>null</li>
     * <li>bytes</li>
     * <li>date</li>
     * <li>time</li>
     * <li>timestamp</li>
     * <li>row</li>
     * </ul>
     */
    @NotBlank(message = "列类型不能为空")
    private SeaTunnelDataType type;
    /**
     * 列长度
     */
    private Integer columnLength;
    /**
     * 列精度
     */
    private Integer columnScale;
    /**
     * 是否可为空
     */
    private boolean nullable = true;
    /**
     * 描述
     */
    private String comment;

    @JsonIgnore
    private boolean isArray = false;

    /**
     * 将字段定义转换为列
     *
     * @param field 字段定义
     * @return 列
     */
    public static Column of(DataModelField field) {
        Column column = new Column();
        column.setName(field.getEnName());
        column.setType(SeaTunnelDataType.mapping(field.getType()));
        if (column.getType().equals(SeaTunnelDataType.DECIMAL)) {
            if (field.getAdvanceConfig() instanceof DecimalAdvanceConfig config) {
                column.setColumnLength(config.getAccuracy());
                column.setColumnScale(config.getScale());
            } else {
                //没有精度配置时的默认值
                column.setColumnLength(10);
                column.setColumnScale(2);
            }
        }
        column.setNullable(field.isNullable());
        column.setArray(field.isMultiValue());
        return column;
    }

    /**
     * 该字段是否时向量类型
     *
     * @return 是否是向量类型
     */
    public boolean isVectorType() {
        return type == SeaTunnelDataType.BYTE_VECTOR || type == SeaTunnelDataType.FLOAT_VECTOR
            || type == SeaTunnelDataType.INT_VECTOR;
    }

    /**
     * 该字段是否是数值类型
     *
     * @return 该字段是否是数值类型
     */
    public boolean isDecimal() {
        return SeaTunnelDataType.DECIMAL.equals(type);
    }
}
