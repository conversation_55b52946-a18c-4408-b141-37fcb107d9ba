package com.trs.moye.storage.engine.seatunnel.job.config.sink;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * RocketMq存储配置
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class RocketMqSinkConfig extends SeatunnelSinkConfig {

    /**
     * 插件名称
     */
    @JsonProperty("plugin_name")
    @Builder.Default
    private String pluginName = "RocketMQ";

    /**
     * RocketMQ topic
     */
    @JsonProperty("topic")
    private String topic;

    /**
     * RocketMQ 名称服务器集群地址。
     */
    @JsonProperty("name.srv.addr")
    private String nameServerAddr;

    /**
     * 是否开启acl
     */
    @JsonProperty("acl.enabled")
    private Boolean aclEnabled;

    /**
     * 访问key
     */
    @JsonProperty("access.key")
    private String accessKey;

    /**
     * 密钥
     */
    @JsonProperty("secret.key")
    private String secretKey;

    /**
     * 消息格式
     * <p>默认为json, 可选项为 text、canal_json、debezium_json、ogg_json、avro</p>
     */
    @JsonProperty("format")
    @Builder.Default
    private String format = "json";
    @JsonProperty("storage_name")
    private String storageName;
}
